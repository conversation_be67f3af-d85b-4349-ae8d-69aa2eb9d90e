#!/usr/bin/env python3
"""
Script to create a log with a trace containing several child spans.

This demonstrates hierarchical logging where:
- A parent span represents the main task
- Child spans represent sub-tasks or steps within the main task
- Each span can have its own input, output, scores, and metadata

Usage:
    python trace_with_spans.py

Make sure to set your BRAINTRUST_API_KEY environment variable.
"""

import time
import braintrust


def main():
    # Initialize logger for a project
    logger = braintrust.init_logger(project="pedro-repro4667")
    
    # Create a parent span for the main task
    with logger.start_span(name="customer_support_interaction") as main_span:
        # Log the initial input for the main task
        main_span.log(
            input="Customer asks: 'My order hasn't arrived yet, can you help me track it?'",
            metadata={
                "customer_id": "cust_12345",
                "interaction_type": "support_request",
                "channel": "chat"
            }
        )
        
        # Child span 1: Intent classification
        with main_span.start_span(name="intent_classification") as intent_span:
            intent_span.log(
                input="My order hasn't arrived yet, can you help me track it?",
                output="order_tracking",
                scores={
                    "confidence": 0.95,
                    "processing_time": 0.12
                },
                metadata={
                    "model": "intent-classifier-v2",
                    "possible_intents": ["order_tracking", "complaint", "refund_request"]
                }
            )
        
        # Child span 2: Order lookup
        with main_span.start_span(name="order_lookup") as lookup_span:
            lookup_span.log(
                input={
                    "customer_id": "cust_12345",
                    "intent": "order_tracking"
                },
                output={
                    "order_id": "ORD-789123",
                    "status": "shipped",
                    "tracking_number": "1Z999AA1234567890",
                    "estimated_delivery": "2024-01-18"
                },
                scores={
                    "lookup_success": 1.0
                },
                metadata={
                    "database_query_time": 0.08,
                    "cache_hit": False
                }
            )
        
        # Child span 3: Response generation
        with main_span.start_span(name="response_generation") as response_span:
            response_span.log(
                input={
                    "intent": "order_tracking",
                    "order_data": {
                        "order_id": "ORD-789123",
                        "status": "shipped",
                        "tracking_number": "1Z999AA1234567890",
                        "estimated_delivery": "2024-01-18"
                    }
                },
                output="I found your order ORD-789123! Good news - it has been shipped and is on its way to you. Your tracking number is 1Z999AA1234567890, and the estimated delivery date is January 18th, 2024. You can track its progress using the tracking number on the carrier's website.",
                scores={
                    "helpfulness": 0.92,
                    "accuracy": 0.98,
                    "tone_appropriateness": 0.89
                },
                metadata={
                    "model": "response-generator-v3",
                    "template_used": "order_tracking_success",
                    "generation_time_ms": 156
                }
            )
        
        # Child span 4: Sentiment analysis (optional step)
        with main_span.start_span(name="sentiment_analysis") as sentiment_span:
            sentiment_span.log(
                input="My order hasn't arrived yet, can you help me track it?",
                output={
                    "sentiment": "neutral_concerned",
                    "emotion": "worried",
                    "urgency_level": "medium"
                },
                scores={
                    "confidence": 0.87,
                    "sentiment_score": -0.2  # Slightly negative due to concern
                },
                metadata={
                    "model": "sentiment-analyzer-v1",
                    "detected_emotions": ["worried", "hopeful"]
                }
            )
        
        # Log the final result on the main span
        main_span.log(
            output="I found your order ORD-789123! Good news - it has been shipped and is on its way to you. Your tracking number is 1Z999AA1234567890, and the estimated delivery date is January 18th, 2024. You can track its progress using the tracking number on the carrier's website.",
            expected="Helpful response with order tracking information",
            scores={
                "overall_satisfaction": 0.91,
                "resolution_success": 1.0,
                "total_processing_time_ms": 568
            },
            metadata={
                "interaction_resolved": True,
                "follow_up_needed": False,
                "customer_satisfaction_predicted": "high"
            },
            tags=["customer_support", "order_tracking", "successful_resolution"]
        )
    
    print("Created trace with main span 'customer_support_interaction' and 4 child spans:")
    print("  1. intent_classification")
    print("  2. order_lookup") 
    print("  3. response_generation")
    print("  4. sentiment_analysis")
    
    # Flush to ensure all logs are sent to the server
    logger.flush()
    print("\nTrace and all spans have been sent to Braintrust server")
    print("Check your Braintrust dashboard to see the hierarchical trace structure")


if __name__ == "__main__":
    main()
